{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\GitHub\\morocco_sports\\android\\app\\.cxx\\RelWithDebInfo\\4j1z1s27\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\GitHub\\morocco_sports\\android\\app\\.cxx\\RelWithDebInfo\\4j1z1s27\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}