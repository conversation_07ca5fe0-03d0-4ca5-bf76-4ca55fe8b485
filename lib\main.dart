import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:morocco_sports/obboarding_screen.dart';
import 'package:morocco_sports/language_manager.dart';
import 'package:morocco_sports/theme_manager.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => LanguageManager()),
        ChangeNotifierProvider(create: (context) => ThemeManager()),
      ],
      child: Consumer2<LanguageManager, ThemeManager>(
        builder: (context, languageManager, themeManager, child) {
          return MaterialApp(
            debugShowCheckedModeBanner: false,
            title: 'Morocco Sports',
            locale: languageManager.currentLocale,
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: LanguageManager.supportedLocales,
            theme: ThemeManager.lightTheme,
            darkTheme: ThemeManager.darkTheme,
            themeMode: themeManager.currentThemeMode,
            home: const OnboardingScreen(),
          );
        },
      ),
    );
  }
}


